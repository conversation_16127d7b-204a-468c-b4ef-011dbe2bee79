// File: lib/features/validation/presentation/screens/riverpod_validation_dashboard_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/app_card.dart';
import '../../../../core/utils/logger.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../top_ups/domain/models/top_up.dart';
import '../../domain/models/integrity_report.dart';
import '../../domain/models/validation_issue.dart';
import '../controllers/validation_dashboard_controller.dart';
import '../providers/validation_provider.dart';
import '../widgets/issue_card.dart';
import '../../../../core/widgets/lekky_button.dart';
import '../../../entries/presentation/dialogs/edit_entry_dialog.dart';
import '../../../../core/providers/settings_provider.dart';

/// The Riverpod Validation Dashboard screen
class RiverpodValidationDashboardScreen extends ConsumerWidget {
  /// Constructor
  const RiverpodValidationDashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final validationAsync = ref.watch(riverpodValidationProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Data Validation'),
        backgroundColor: AppColors.primary,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
          tooltip: 'Back to History',
        ),
        actions: _buildAppBarActions(context, ref, validationAsync),
      ),
      body: validationAsync.when(
        data: (validationState) => _buildBody(context, ref, validationState),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stackTrace) =>
            _buildErrorState(context, ref, error.toString()),
      ),
    );
  }

  /// Build the app bar actions
  List<Widget> _buildAppBarActions(BuildContext context, WidgetRef ref,
      AsyncValue<ValidationState> validationAsync) {
    return [
      validationAsync.when(
        data: (validationState) =>
            _buildFilterIcon(context, ref, validationState),
        loading: () => const SizedBox.shrink(),
        error: (_, __) => const SizedBox.shrink(),
      ),
      IconButton(
        icon: const Icon(Icons.refresh),
        tooltip: 'Refresh',
        onPressed: () =>
            ref.read(riverpodValidationProvider.notifier).refresh(),
      ),
    ];
  }

  /// Build the filter icon with dynamic styling and animation
  Widget _buildFilterIcon(
      BuildContext context, WidgetRef ref, ValidationState validationState) {
    final filterColor = _getFilterIconColor(validationState.filterType);
    final isFilterActive =
        validationState.filterType != ValidationIssueFilterType.all;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      margin: const EdgeInsets.all(8),
      decoration: isFilterActive
          ? BoxDecoration(
              color: filterColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            )
          : null,
      child: AnimatedScale(
        scale: isFilterActive ? 1.1 : 1.0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        child: IconButton(
          icon: Icon(
            Icons.filter_list,
            color: isFilterActive ? filterColor : Colors.white,
          ),
          tooltip: 'Filter',
          onPressed: () => _showFilterDialog(context, ref, validationState),
        ),
      ),
    );
  }

  /// Get filter icon color based on filter type
  Color _getFilterIconColor(ValidationIssueFilterType filterType) {
    switch (filterType) {
      case ValidationIssueFilterType.all:
        return Colors.white;
      case ValidationIssueFilterType.highSeverity:
        return Colors.red;
      case ValidationIssueFilterType.mediumSeverity:
        return Colors.orange;
      case ValidationIssueFilterType.lowSeverity:
        return Colors.blue;
    }
  }

  /// Build the body of the screen
  Widget _buildBody(
      BuildContext context, WidgetRef ref, ValidationState validationState) {
    if (validationState.errorMessage != null) {
      return _buildErrorState(context, ref, validationState.errorMessage!);
    }

    if (validationState.filteredIssues.isEmpty) {
      return _buildEmptyState(context, ref);
    }

    return Column(
      children: [
        // Summary card
        if (validationState.integrityReport != null)
          _buildSummaryCard(validationState.integrityReport!),

        // Issues list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: validationState.filteredIssues.length,
            itemBuilder: (context, index) {
              final issue = validationState.filteredIssues[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: IssueCard(
                  issue: issue,
                  onFix: (issue) => _showEditEntry(context, ref, issue),
                  onIgnore: null,
                  onTap: (issue) => _showIssueDetails(context, ref, issue),
                  isSelected: false,
                  selectionMode: false,
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// Build the error state
  Widget _buildErrorState(
      BuildContext context, WidgetRef ref, String errorMessage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            errorMessage,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.red,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          LekkyButton(
            text: 'Retry',
            type: LekkyButtonType.primary,
            onPressed: () =>
                ref.read(riverpodValidationProvider.notifier).refresh(),
          ),
        ],
      ),
    );
  }

  /// Build the empty state
  Widget _buildEmptyState(BuildContext context, WidgetRef ref) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.check_circle_outline,
            color: Colors.green,
            size: 64,
          ),
          const SizedBox(height: 16),
          const Text(
            'No validation issues found',
            style: AppTextStyles.titleMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          const Text(
            'All your data is valid and consistent',
            style: AppTextStyles.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          LekkyButton(
            text: 'Refresh',
            type: LekkyButtonType.primary,
            onPressed: () =>
                ref.read(riverpodValidationProvider.notifier).refresh(),
          ),
        ],
      ),
    );
  }

  /// Build the summary card
  Widget _buildSummaryCard(IntegrityReport report) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: AppCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Data Integrity Summary',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Divider(),
            const SizedBox(height: 8),

            // Entries checked
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Entries Checked:'),
                Text(
                  '${report.totalEntriesChecked}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Valid entries
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Valid Entries:'),
                Text(
                  '${report.validEntriesCount} (${report.validPercentage.toStringAsFixed(1)}%)',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Invalid entries
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Invalid Entries:'),
                Text(
                  '${report.invalidEntriesCount} (${report.invalidPercentage.toStringAsFixed(1)}%)',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Issues by severity
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('High Severity Issues:'),
                Text(
                  '${report.highSeverityCount}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Medium Severity Issues:'),
                Text(
                  '${report.mediumSeverityCount}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Low Severity Issues:'),
                Text(
                  '${report.lowSeverityCount}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Last check time
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Last Check:'),
                Text(
                  _formatDateTime(report.generatedAt),
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Calculate responsive dialog width
  double _getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return screenWidth < 600 ? screenWidth * 0.95 : 500.0;
  }

  /// Show the filter dialog
  void _showFilterDialog(
      BuildContext context, WidgetRef ref, ValidationState validationState) {
    final screenWidth = MediaQuery.of(context).size.width;
    final dialogWidth = _getDialogWidth(context);
    final horizontalPadding = (screenWidth - dialogWidth) / 2;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 24,
        insetPadding: EdgeInsets.symmetric(
          horizontal: horizontalPadding,
          vertical: 28,
        ),
        child: Container(
          width: dialogWidth,
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDialogHeader(context),
              const SizedBox(height: 24),
              _buildFilterSeverityDropdown(context, ref, validationState),
              const SizedBox(height: 32),
              _buildActionButtons(context),
            ],
          ),
        ),
      ),
    );
  }

  /// Build dialog header
  Widget _buildDialogHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(
          Icons.tune,
          color: theme.colorScheme.primary,
          size: 24,
        ),
        const SizedBox(width: 8),
        Text(
          'Filter Issues',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const Spacer(),
        IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
          tooltip: 'Close',
        ),
      ],
    );
  }

  /// Build filter severity dropdown
  Widget _buildFilterSeverityDropdown(
      BuildContext context, WidgetRef ref, ValidationState validationState) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Filter by Severity',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 4),
          decoration: BoxDecoration(
            border: Border.all(
              color: theme.colorScheme.outline.withOpacity(0.5),
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<ValidationIssueFilterType>(
              value: validationState.filterType,
              isExpanded: true,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              borderRadius: BorderRadius.circular(8),
              items: [
                ValidationIssueFilterType.all,
                ValidationIssueFilterType.highSeverity,
                ValidationIssueFilterType.mediumSeverity,
                ValidationIssueFilterType.lowSeverity,
              ].map((type) {
                String label;
                switch (type) {
                  case ValidationIssueFilterType.all:
                    label = 'All Issues';
                    break;
                  case ValidationIssueFilterType.highSeverity:
                    label = 'High Severity';
                    break;
                  case ValidationIssueFilterType.mediumSeverity:
                    label = 'Medium Severity';
                    break;
                  case ValidationIssueFilterType.lowSeverity:
                    label = 'Low Severity';
                    break;
                  default:
                    label = 'Unknown';
                    break;
                }

                return DropdownMenuItem<ValidationIssueFilterType>(
                  value: type,
                  child: Text(label),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  ref
                      .read(riverpodValidationProvider.notifier)
                      .setFilterType(value);
                  Navigator.of(context).pop();
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  /// Build action buttons
  Widget _buildActionButtons(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Expanded(
          child: LekkyButton(
            text: 'Cancel',
            type: LekkyButtonType.secondary,
            size: LekkyButtonSize.compact,
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
      ],
    );
  }

  /// Show edit entry dialog
  Future<void> _showEditEntry(
      BuildContext context, WidgetRef ref, ValidationIssue issue) async {
    Logger.info(
        'RiverpodDashboard: Attempting to show edit entry for issue: entryId=${issue.entryId}, type=${issue.type}');

    final entry = await ref
        .read(riverpodValidationProvider.notifier)
        .getEntryForIssue(issue);

    if (entry == null) {
      Logger.warning(
          'RiverpodDashboard: Entry not found for issue entryId: ${issue.entryId}');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Entry not found')),
        );
      }
      return;
    }

    if (context.mounted) {
      final settingsAsync = ref.read(settingsProvider);
      final currencySymbol = settingsAsync.when(
        data: (settings) => settings.currencySymbol,
        loading: () => '₦', // Default fallback
        error: (_, __) => '₦', // Default fallback
      );

      // Navigate to edit entry based on entry type
      if (entry is MeterReading) {
        await showDialog(
          context: context,
          builder: (context) => EditEntryDialog(
            meterReading: entry,
            topUp: null,
            currencySymbol: currencySymbol,
            onEntryUpdated: () =>
                ref.read(riverpodValidationProvider.notifier).refresh(),
            onEntryDeleted: () =>
                ref.read(riverpodValidationProvider.notifier).refresh(),
          ),
        );
      } else if (entry is TopUp) {
        await showDialog(
          context: context,
          builder: (context) => EditEntryDialog(
            meterReading: null,
            topUp: entry,
            currencySymbol: currencySymbol,
            onEntryUpdated: () =>
                ref.read(riverpodValidationProvider.notifier).refresh(),
            onEntryDeleted: () =>
                ref.read(riverpodValidationProvider.notifier).refresh(),
          ),
        );
      }
    }
  }

  /// Show issue details
  void _showIssueDetails(
      BuildContext context, WidgetRef ref, ValidationIssue issue) {
    // TODO: Implement issue details dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Issue details: ${issue.message}')),
    );
  }

  /// Format a date time for display
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
